#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整测试 dynamic_gap_detector.py 的列缺失问题修复
模拟真实的数据处理场景
"""

import sys
import os
import pandas as pd
from datetime import time

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'test'))

def create_test_data():
    """创建测试数据"""
    # 创建有完整列的数据
    complete_data = pd.DataFrame({
        '名称': [f'股票{i}' for i in range(1, 101)],
        '今日主力净流入-净额': [1000000 - i*10000 for i in range(100)],
        '今日涨跌幅': [5.0 - i*0.1 for i in range(100)],
        '今日主力净流入-净占比': [10.0 - i*0.1 for i in range(100)]
    })
    
    # 创建缺少涨跌幅列的数据
    incomplete_data = pd.DataFrame({
        '名称': [f'股票{i}' for i in range(1, 101)],
        '今日主力净流入-净额': [1000000 - i*10000 for i in range(100)],
        '今日主力净流入-净占比': [10.0 - i*0.1 for i in range(100)]
    })
    
    return complete_data, incomplete_data

def test_data_processing():
    """测试数据处理功能"""
    print("测试数据处理功能...")
    
    complete_data, incomplete_data = create_test_data()
    
    # 测试1: 完整数据处理
    print("\n测试1: 完整数据处理")
    try:
        # 模拟原始过滤逻辑（修复后）
        if '今日涨跌幅' in complete_data.columns:
            positive_stocks = complete_data[
                (complete_data['今日主力净流入-净额'] > 0) &
                (complete_data['今日涨跌幅'] >= 0)
            ]
        else:
            positive_stocks = complete_data[
                complete_data['今日主力净流入-净额'] > 0
            ]
        
        print(f"✅ 完整数据：过滤后 {len(positive_stocks)} 只股票")
        
    except Exception as e:
        print(f"❌ 完整数据处理失败: {e}")
    
    # 测试2: 不完整数据处理
    print("\n测试2: 不完整数据处理")
    try:
        # 模拟修复后的过滤逻辑
        if '今日涨跌幅' in incomplete_data.columns:
            positive_stocks = incomplete_data[
                (incomplete_data['今日主力净流入-净额'] > 0) &
                (incomplete_data['今日涨跌幅'] >= 0)
            ]
        else:
            positive_stocks = incomplete_data[
                incomplete_data['今日主力净流入-净额'] > 0
            ]
        
        print(f"✅ 不完整数据：过滤后 {len(positive_stocks)} 只股票")
        
    except Exception as e:
        print(f"❌ 不完整数据处理失败: {e}")

def test_module_integration():
    """测试模块集成"""
    print("\n测试模块集成...")
    
    try:
        from dynamic_gap_detector import analyze_stock_flow_gap, parse_stock_flow_data
        
        # 创建测试CSV文件
        complete_data, incomplete_data = create_test_data()
        
        # 测试完整数据
        complete_csv = 'test_complete_data.csv'
        complete_data.to_csv(complete_csv, index=False, encoding='utf-8-sig')
        
        parsed_complete = parse_stock_flow_data(complete_csv, 'stock_flow')
        if parsed_complete is not None:
            print(f"✅ 完整数据解析成功：{len(parsed_complete)} 只股票")
            
            # 测试分析功能
            try:
                result = analyze_stock_flow_gap(parsed_complete, time(14, 30, 0))
                print("✅ 完整数据分析成功")
            except Exception as e:
                print(f"❌ 完整数据分析失败: {e}")
        
        # 测试不完整数据
        incomplete_csv = 'test_incomplete_data.csv'
        incomplete_data.to_csv(incomplete_csv, index=False, encoding='utf-8-sig')
        
        parsed_incomplete = parse_stock_flow_data(incomplete_csv, 'stock_flow')
        if parsed_incomplete is not None:
            print(f"✅ 不完整数据解析成功：{len(parsed_incomplete)} 只股票")
            
            # 测试分析功能
            try:
                result = analyze_stock_flow_gap(parsed_incomplete, time(14, 30, 0))
                print("✅ 不完整数据分析成功")
            except Exception as e:
                print(f"❌ 不完整数据分析失败: {e}")
        
        # 清理测试文件
        for file in [complete_csv, incomplete_csv]:
            if os.path.exists(file):
                os.remove(file)
                
    except Exception as e:
        print(f"❌ 模块集成测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("=" * 50)
    print("dynamic_gap_detector.py 列缺失问题修复测试")
    print("=" * 50)
    
    test_data_processing()
    test_module_integration()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)

if __name__ == "__main__":
    main()
