#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
V5.0优化测试脚本
测试新增的绝对能量验证和重构的评分体系
"""

import pandas as pd
import numpy as np
from datetime import datetime, time
from dynamic_gap_detector import StockFlowIgnitionDetector, MIN_ABSOLUTE_INFLOW_CHANGE, MIN_RANK_BENCHMARK_RATIO

def create_test_data():
    """创建测试数据"""
    # 创建一个包含50只股票的测试数据
    data = []
    for i in range(50):
        stock_name = f"测试股票{i+1:02d}"
        # 前10名资金量较大，后面递减
        if i < 10:
            net_inflow = 50000000 - i * 3000000  # 5000万到2300万
        else:
            net_inflow = 20000000 - (i-10) * 500000  # 2000万递减
        
        data.append({
            '名称': stock_name,
            '代码': f"00{i+1:04d}",
            '今日主力净流入-净额': net_inflow,
            '今日超大单净流入-净额': net_inflow * 0.7,  # 假设超大单占70%
            '今日涨跌幅': np.random.uniform(-2, 8)  # 随机涨跌幅
        })
    
    return pd.DataFrame(data)

def create_previous_data_snapshot(current_data):
    """创建上一时刻的数据快照（模拟资金流入变化）"""
    snapshot = {}
    for idx, stock in current_data.iterrows():
        # 模拟上一时刻的资金流入（当前值减去一个增量）
        previous_inflow = stock['今日主力净流入-净额'] - np.random.uniform(1000000, 10000000)
        previous_super_large = stock['今日超大单净流入-净额'] - np.random.uniform(500000, 7000000)
        
        snapshot[stock['名称']] = {
            'rank': idx + 1 + np.random.randint(-5, 6),  # 模拟排名变化
            'net_inflow': previous_inflow,
            'super_large_net_inflow': previous_super_large,
            'timestamp': datetime.now().time()
        }
    
    return snapshot

def test_v5_absolute_energy_validation():
    """测试V5.0绝对能量验证功能"""
    print("=" * 60)
    print("测试V5.0绝对能量验证功能")
    print("=" * 60)
    
    # 创建测试数据
    current_data = create_test_data()
    current_time = datetime.now().time()
    
    # 创建检测器
    detector = StockFlowIgnitionDetector()
    detector.previous_snapshot = create_previous_data_snapshot(current_data)
    
    # 测试第一只股票（应该满足所有条件）
    stock = current_data.iloc[0]
    previous_data = detector.previous_snapshot[stock['名称']]
    
    # 计算指标
    current_rank = 1
    rv = detector.calculate_rank_velocity(current_rank, previous_data['rank'])
    wra = detector.calculate_weighted_rank_acceleration(rv, current_rank)
    ct = detector.calculate_capital_thrust(stock['今日主力净流入-净额'], previous_data['net_inflow'])
    pf = detector.calculate_real_purity_of_force(
        stock['今日主力净流入-净额'], previous_data['net_inflow'],
        stock['今日超大单净流入-净额'], previous_data['super_large_net_inflow']
    )
    
    # 模拟动态阈值
    dynamic_thresholds = {
        'dynamic_wra_threshold': 0.1,
        'dynamic_ct_threshold': 1000,
        'data_points': 100
    }
    
    print(f"测试股票: {stock['名称']}")
    print(f"当前排名: {current_rank}")
    print(f"WRA: {wra:.3f}")
    print(f"CT: {ct:.0f}")
    print(f"PF: {pf:.3f}")
    print(f"资金增量: {(stock['今日主力净流入-净额'] - previous_data['net_inflow'])/1e6:.1f}万")
    print(f"最低门槛: {MIN_ABSOLUTE_INFLOW_CHANGE/1e6:.0f}万")
    
    # 测试V5.0信号判断
    is_signal = detector._is_v5_ignition_signal(wra, ct, pf, current_rank, dynamic_thresholds, stock, previous_data)
    print(f"V5.0信号判断结果: {'✅ 通过' if is_signal else '❌ 未通过'}")
    
    # 测试V5.0评分
    if is_signal:
        score = detector._calculate_v5_ignition_score(wra, ct, pf, current_rank, dynamic_thresholds)
        print(f"V5.0评分: {score:.2f}/10")
    
    print()

def test_v5_scoring_system():
    """测试V5.0重构的评分体系"""
    print("=" * 60)
    print("测试V5.0重构的评分体系")
    print("=" * 60)
    
    detector = StockFlowIgnitionDetector()
    
    # 测试不同CT绝对量的评分
    test_cases = [
        {"ct": 60000, "desc": "超大资金冲击(6亿/分钟)"},
        {"ct": 30000, "desc": "大资金冲击(3亿/分钟)"},
        {"ct": 15000, "desc": "中等资金冲击(1.5亿/分钟)"},
        {"ct": 8000, "desc": "小资金冲击(8000万/分钟)"},
        {"ct": 3000, "desc": "微量资金冲击(3000万/分钟)"}
    ]
    
    dynamic_thresholds = {
        'dynamic_wra_threshold': 0.1,
        'dynamic_ct_threshold': 1000,
        'data_points': 100
    }
    
    for case in test_cases:
        ct = case["ct"]
        wra = 0.5  # 固定WRA
        pf = 0.8   # 固定PF
        current_rank = 10  # 固定排名
        
        score = detector._calculate_v5_ignition_score(wra, ct, pf, current_rank, dynamic_thresholds)
        print(f"{case['desc']}: CT={ct} -> 评分={score:.2f}/10")
    
    print()

def test_v5_comprehensive():
    """V5.0综合测试"""
    print("=" * 60)
    print("V5.0综合功能测试")
    print("=" * 60)
    
    # 创建测试数据
    current_data = create_test_data()
    current_time = datetime.now().time()
    
    # 创建检测器并运行检测
    detector = StockFlowIgnitionDetector()
    
    # 第一次运行（建立基准）
    signals1 = detector.detect_ignition_signals(current_data, current_time)
    print(f"第一次检测: {len(signals1)} 个信号")
    
    # 模拟数据变化，第二次运行
    # 增加前几只股票的资金流入
    for i in range(5):
        current_data.iloc[i, current_data.columns.get_loc('今日主力净流入-净额')] += np.random.uniform(5000000, 15000000)
        current_data.iloc[i, current_data.columns.get_loc('今日超大单净流入-净额')] += np.random.uniform(3000000, 10000000)
    
    signals2 = detector.detect_ignition_signals(current_data, current_time)
    print(f"第二次检测: {len(signals2)} 个信号")
    
    # 显示检测到的信号
    for i, signal in enumerate(signals2[:3]):  # 显示前3个信号
        print(f"\n信号 {i+1}:")
        print(f"  股票: {signal['stock_name']}")
        print(f"  排名: {signal['old_rank']} -> {signal['new_rank']}")
        print(f"  WRA: {signal['wra']:.3f}")
        print(f"  CT: {signal['ct']:.0f}")
        print(f"  PF: {signal['pf']:.3f}")
        print(f"  评分: {signal['score']:.2f}/10")
        print(f"  信号类型: {signal['signal_type']}")

if __name__ == "__main__":
    print("V5.0优化功能测试")
    print("测试内容：绝对能量验证 + 重构评分体系")
    print()
    
    try:
        test_v5_absolute_energy_validation()
        test_v5_scoring_system()
        test_v5_comprehensive()
        
        print("=" * 60)
        print("✅ V5.0优化测试完成！")
        print("主要改进:")
        print("1. 新增绝对能量验证，过滤小资金异动")
        print("2. 重构评分体系，平衡技巧分和能量分")
        print("3. 引入CT绝对量评分，突出大资金优势")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
